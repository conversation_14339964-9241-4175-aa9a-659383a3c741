import { useActions, useLocalStorageState } from '@topwrite/common';
import { Dropdown } from 'react-bootstrap';
import { GoPlus } from 'react-icons/go';
import { VscChromeClose, VscHistory, VscReply, VscSettingsGear } from 'react-icons/vsc';
import Button from '../../components/button';
import Tooltip from '../../components/tooltip';
import useFormatMessage from '../../lib/use-format-message';
import { useContext } from './context';

export default function Action() {
    const { reset, windowState, setWindowState, authorized } = useContext();
    const t = useFormatMessage();
    const { updateConfig } = useActions('book');
    const [, setAssistantWindow] = useLocalStorageState('assistant.window', true);

    if (windowState !== 'chat') {
        return <div className={'d-flex gap-1'}>
            <Tooltip tooltip={t('assistant.back')}>
                <Button variant='light' onClick={() => setWindowState('chat')}>
                    <VscReply />
                </Button>
            </Tooltip>
        </div>;
    }

    return <div className={'d-flex'}>
        {authorized && <Tooltip tooltip={'assistant.new'}>
            <Button variant='light' onClick={() => reset()}><GoPlus /></Button>
        </Tooltip>}
        {authorized && <Tooltip tooltip={'assistant.history'}>
            <Button variant='light' onClick={() => setWindowState('history')}>
                <VscHistory />
            </Button>
        </Tooltip>}
        {authorized && <Dropdown>
            <Dropdown.Toggle variant='light'><VscSettingsGear /></Dropdown.Toggle>
            <Dropdown.Menu>
                <Dropdown.Item>
                    设置
                </Dropdown.Item>
                {typeof authorized === 'string' && <Dropdown.Item onClick={() => {
                    updateConfig((config) => {
                        config.setValue(['assistant', 'token'], undefined);
                    });
                }}>
                    注销
                </Dropdown.Item>}
            </Dropdown.Menu>
        </Dropdown>}
        <Tooltip tooltip={t('assistant.close')}>
            <Button variant='light' onClick={() => setAssistantWindow(false)}>
                <VscChromeClose />
            </Button>
        </Tooltip>
    </div>;
}
